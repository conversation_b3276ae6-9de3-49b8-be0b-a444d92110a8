'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Lightbulb, Plus, Search, Trophy } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PERMISSIONS, hasPermission } from '@/lib/types/permissions';
import {
  GetIdeas,
  GetIdeaStats,
  GetLeaderboard,
  toggleIdeaLike,
  updateIdeaStatus,
  addIdeaComment,
  deleteIdea,
  type Idea,
  type IdeaStatus,
} from '@/api/innovation-hub/data';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { IdeaCard } from './components/idea-card';
import { CommentModal } from './components/comment-modal';
import { LoadingState } from './components/loading-state';
import { ErrorState } from './components/error-state';
import { StatsCards } from './components/stats-cards';
import { EmptyState } from './components/empty-state';
import NewIdea from './components/new-idea';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import DateRangeFilter from '../common/date-range-filter';
import MonthYearFilter from '../common/month-year-filter';
import { Input } from '../ui/input';

export default function InnovationHubContent() {
  const searchParams = useSearchParams();
  const { stats, statsLoading, statsError, statsMutate } = GetIdeaStats();
  const [activeTab, setActiveTab] = useState<'all' | 'my' | 'leaderboard'>(
    'all'
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isCommentModalOpen, setCommentModalOpen] = useState(false);
  const [selectedIdeaForComments, setSelectedIdeaForComments] =
    useState<Idea | null>(null);
  const [openNewIdeaModal, setOpenNewIdeaModal] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [selectedMonth, setSelectedMonth] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<string>('');

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const canEdit = hasPermission(PERMISSIONS.HUB_EDIT);

  // Build query parameters for API call
  useEffect(() => {
    const params = [];
    const slug = searchParams.get('slug');

    if (statusFilter && statusFilter !== 'all') {
      params.push(`status=${statusFilter}`);
    }
    if (activeTab === 'my') {
      params.push(`myIdeas=true`);
    }

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (slug) {
      params.push(`slug=${slug}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [
    debouncedSearchTerm,
    statusFilter,
    activeTab,
    startDate,
    endDate,
    searchParams,
    setQueryParam,
  ]);

  const hasActiveFilters = debouncedSearchTerm || statusFilter !== 'all' || startDate || endDate || activeTab === 'my' || searchParams.get('slug');

  // Fetch ideas data
  const { ideas, isLoading, error, mutate } = GetIdeas(
    `?page=${currentPage}&limit=${pageSize}&${queryParam}`
  );

  // Fetch leaderboard data
  const {
    leaderboard,
    leaderboardLoading,
    leaderboardError,
    leaderboardMutate,
  } = GetLeaderboard(`?month=${selectedMonth}&year=${selectedYear}`);

  // Extract pagination data from API response
  const totalPages = ideas?.totalPages || 1;
  const total = ideas?.total || 0;
  const ideasData = ideas?.ideas || [];
  const leaderboardData = leaderboard?.ideas || [];

  const handleMutate = () => {
    mutate();
    leaderboardMutate();
    statsMutate();
  };

  const filterOptions = [
    { value: 'all', label: 'All' },
    { value: 'DRAFT', label: 'Drafts' },
    { value: 'PENDING_REVIEW', label: 'Pending Review' },
    { value: 'ACCEPTED', label: 'Accepted' },
    { value: 'REJECTED', label: 'Rejected' },
    { value: 'IMPLEMENTED', label: 'Implemented' },
  ];

  // Event handlers
  const handleLike = async (id: number) => {
    try {
      await toggleIdeaLike(id);
      handleMutate();
    } catch (error) {
      console.error('Error liking idea:', error);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteIdea(id);
      handleMutate();
    } catch (error) {
      console.error('Error deleting idea:', error);
    }
  };

  const handleStatusUpdate = async (id: number, newStatus: IdeaStatus) => {
    try {
      await updateIdeaStatus(id, newStatus);
      handleMutate();
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleOpenCommentModal = (idea: Idea) => {
    setSelectedIdeaForComments(idea);
    setCommentModalOpen(true);
  };

  const handleCommentSubmit = async (commentText: string) => {
    if (!selectedIdeaForComments) return;

    try {
      await addIdeaComment(selectedIdeaForComments.id, commentText);
      setCommentModalOpen(false);
      handleMutate();
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  const handleMonthYearChange = (month: string, year: string) => {
    setSelectedMonth(month);
    setSelectedYear(year);
  };

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Lightbulb className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Innovation Hub
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Explore, share, and collaborate on cutting-edge ideas.
                <br />
                The innovation hub is a powerful initiative that can help
                transform our healthcare delivery, improve patient outcomes,
                reduce costs, and enhance staff experience.
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 sm:border border-gray-200 dark:border-[#1F1F23]">
          <div className="space-y-6">
            <div className="flex flex-wrap items-center justify-between space-y-2">
              <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                <Button
                  variant={activeTab === 'all' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => {
                    setActiveTab('all');
                  }}
                  className="px-4 py-2"
                >
                  All Ideas
                </Button>
                <Button
                  variant={activeTab === 'my' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => {
                    setActiveTab('my');
                  }}
                  className="px-4 py-2 "
                >
                  My Ideas
                </Button>
                <Button
                  size="icon"
                  variant={activeTab === 'leaderboard' ? 'default' : 'outline'}
                  className="cursor-pointer border dark:border-amber-50 border-amber-400"
                  onClick={() => setActiveTab('leaderboard')}
                >
                  <Trophy className="w-3.5 h-3.5" />
                </Button>
              </div>
              <Button
                size="sm"
                onClick={() => setOpenNewIdeaModal(true)}
                className="cursor-pointer"
              >
                <Plus className="w-3.5 h-3.5" /> New Idea
              </Button>
            </div>
          </div>
          <StatsCards stats={stats} />
          {activeTab !== 'leaderboard' && (
            <div className="flex flex-wrap gap-3 items-center my-2">
              <div className="relative w-full sm:w-64">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Search className="w-4 h-4 text-gray-500" />
                </div>
                <Input
                  type="text"
                  placeholder="Search ideas..."
                  className="pl-10 pr-4 py-2 w-full"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
              </div>
              <Select value={statusFilter} onValueChange={handleStatusChange}>
                <SelectTrigger className="w-full sm:w-[220px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <DateRangeFilter
                onDateRangeChange={handleDateRangeChange}
                className="w-full sm:w-[250px]"
              />
            </div>
          )}
          {activeTab === 'leaderboard' ? (
            <>
              <div className="space-y-2">
                <MonthYearFilter
                  onFilterChange={handleMonthYearChange}
                  className="flex gap-2"
                />
                {leaderboardError ? (
                  <ErrorState
                    error={leaderboardError}
                    onRetry={() => leaderboardMutate()}
                  />
                ) : leaderboardLoading ? (
                  <LoadingState />
                ) : (
                  <>
                    {leaderboard?.ideas.length === 0 ? (
                      <EmptyState
                        title="No Idea Found"
                        description="No Ideas for the selected Month. You can change the month and year to view leaderboard from other months!"
                      />
                    ) : (
                      leaderboardData.map((idea: Idea, index: number) => (
                        <IdeaCard
                          key={idea.id}
                          idea={idea}
                          onLike={handleLike}
                          canEdit={canEdit}
                          onDelete={handleDelete}
                          onStatusUpdate={handleStatusUpdate}
                          onCommentClick={handleOpenCommentModal}
                          isChampion={index === 0}
                        />
                      ))
                    )}
                  </>
                )}
              </div>
            </>
          ) : error ? (
            <ErrorState error={error} onRetry={() => mutate()} />
          ) : isLoading ? (
            <LoadingState />
          ) : ideasData.length === 0 ? (
            <EmptyState
              title={(() => {
                const hasFilters = debouncedSearchTerm || (statusFilter && statusFilter !== 'all') || startDate || endDate || activeTab === 'my' || searchParams.get('slug');
                
                if (!hasFilters) return 'No ideas yet';
                
                const filters = [];
                if (debouncedSearchTerm) filters.push(`search "${debouncedSearchTerm}"`);
                if (searchParams.get('slug')) filters.push('shared link');
                if (statusFilter && statusFilter !== 'all') filters.push(`status "${statusFilter}"`);
                if (activeTab === 'my') filters.push('your ideas');
                if (startDate || endDate) filters.push('date range');
                
                return `No ideas found for ${filters.join(', ')}`;
              })()}
              description={(() => {
                const hasFilters = debouncedSearchTerm || (statusFilter && statusFilter !== 'all') || startDate || endDate || activeTab === 'my' || searchParams.get('slug');
                
                if (!hasFilters) return 'Be the first to share your innovative idea and inspire others!';
                
                return 'Try adjusting your search criteria or filters to find more ideas.';
              })()}
              actionText="Clear Filters"
              onAction={
                (debouncedSearchTerm || (statusFilter && statusFilter !== 'all') || startDate || endDate || activeTab === 'my' || searchParams.get('slug'))
                  ? () => {
                      setSearchQuery('');
                      setActiveTab('all');
                      setStatusFilter('');
                      setStartDate(undefined);
                      setEndDate(undefined);
                      handlePageChange(1);
                      window.history.replaceState({}, '', window.location.pathname);
                    }
                  : undefined
              }
            />
          ) : (
            <>
              <div className="space-y-2">
                {ideasData.map((idea: Idea) => (
                  <IdeaCard
                    key={idea.id}
                    idea={idea}
                    onLike={handleLike}
                    canEdit={canEdit}
                    onDelete={handleDelete}
                    onStatusUpdate={handleStatusUpdate}
                    onCommentClick={handleOpenCommentModal}
                  />
                ))}

                {totalPages > 1 && (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                )}
              </div>
            </>
          )}
        </div>
      </div>

      <CommentModal
        idea={selectedIdeaForComments}
        open={isCommentModalOpen}
        onOpenChange={setCommentModalOpen}
        onCommentSubmit={handleCommentSubmit}
      />

      <NewIdea
        open={openNewIdeaModal}
        setOpen={setOpenNewIdeaModal}
        mutate={handleMutate}
      />
    </>
  );
}
