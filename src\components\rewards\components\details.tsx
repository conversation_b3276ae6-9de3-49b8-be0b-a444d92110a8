import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '@/components/types';
import {
  InputField,
  SwitchField,
  FormRow,
  InputCalendar,
} from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import dayjs from 'dayjs';

interface FormData {
  id: number;
  value: string;
  isActive: boolean;
  validFrom: Date | null;
  validUntil: Date | null;
  maxRewardsPerUser: string;
  maxRewardsPerDay: string;
}

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [editValue, setEditValue] = useState(false);
  const [editDates, setEditDates] = useState(false);
  const [editLimits, setEditLimits] = useState(false);

  const form = useForm<FormData>({
    defaultValues: {
      value: data?.value,
      isActive: data?.isActive,
      validFrom: data?.validFrom ? new Date(data.validFrom) : null,
      validUntil: data?.validUntil ? new Date(data.validUntil) : null,
      maxRewardsPerUser: data?.maxRewardsPerUser?.toString() || '',
      maxRewardsPerDay: data?.maxRewardsPerDay?.toString() || '',
    },
  });

  useEffect(() => {
    if (data) {
      form.reset(data);
    }
  }, [data, form]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/reward/update/`, {
        id: data?.id,
        isActive: data.isActive,
        value: Number(data?.value),
        validFrom: data.validFrom,
        validUntil: data.validUntil,
        maxRewardsPerUser: data.maxRewardsPerUser
          ? Number(data.maxRewardsPerUser)
          : null,
        maxRewardsPerDay: data.maxRewardsPerDay
          ? Number(data.maxRewardsPerDay)
          : null,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        setOpen(false);
        setEditValue(false);
        setEditDates(false);
        setEditLimits(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Reward Details"
      description="View and manage reward details"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <FormRow>
            <div>
              <h3 className="text-sm font-medium">Name</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data?.name}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Event Type</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data?.eventType}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Value Type</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data?.valueType}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">
                Value{' '}
                <span
                  onClick={() => setEditValue(!editValue)}
                  className="text-xs text-[#BD9A3D] underline cursor-pointer"
                >
                  (edit)
                </span>
              </h3>
              {editValue ? (
                <InputField
                  control={form.control}
                  name="value"
                  label="Enter new value"
                  placeholder=""
                  type="number"
                />
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {data?.value}
                </p>
              )}
            </div>
          </FormRow>
          <FormRow>
            <div>
              <h3 className="text-sm font-medium">
                Valid From{' '}
                <span
                  onClick={() => setEditDates(!editDates)}
                  className="text-xs text-[#BD9A3D] underline cursor-pointer"
                >
                  (edit)
                </span>
              </h3>
              {editDates ? (
                <InputCalendar
                  control={form.control}
                  name="validFrom"
                  label="Valid From"
                />
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {data?.validFrom
                    ? dayjs(data.validFrom).format('MMMM D, YYYY')
                    : '-'}
                </p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium">
                Valid Until{' '}
                <span
                  onClick={() => setEditDates(!editDates)}
                  className="text-xs text-[#BD9A3D] underline cursor-pointer"
                >
                  (edit)
                </span>
              </h3>
              {editDates ? (
                <InputCalendar
                  control={form.control}
                  name="validUntil"
                  label="Valid Until"
                />
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {data?.validUntil
                    ? dayjs(data.validUntil).format('MMMM D, YYYY')
                    : '-'}
                </p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium">
                Max Rewards Per User{' '}
                <span
                  onClick={() => setEditLimits(!editLimits)}
                  className="text-xs text-[#BD9A3D] underline cursor-pointer"
                >
                  (edit)
                </span>
              </h3>
              {editLimits ? (
                <InputField
                  control={form.control}
                  name="maxRewardsPerUser"
                  label="Max Rewards Per User"
                  placeholder="Leave empty for unlimited"
                  type="number"
                />
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {data?.maxRewardsPerUser || 'Unlimited'}
                </p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium">
                Max Rewards Per Day{' '}
                <span
                  onClick={() => setEditLimits(!editLimits)}
                  className="text-xs text-[#BD9A3D] underline cursor-pointer"
                >
                  (edit)
                </span>
              </h3>
              {editLimits ? (
                <InputField
                  control={form.control}
                  name="maxRewardsPerDay"
                  label="Max Rewards Per Day"
                  placeholder="Leave empty for unlimited"
                  type="number"
                />
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {data?.maxRewardsPerDay || 'Unlimited'}
                </p>
              )}
            </div>
          </FormRow>
          <SwitchField
            control={form.control}
            name="isActive"
            label="Is Active?"
          />
          <div>
            <h3 className="text-sm font-medium">Description</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data?.description}
            </p>
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default Details;
