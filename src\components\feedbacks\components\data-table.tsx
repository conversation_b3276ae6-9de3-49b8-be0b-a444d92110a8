'use client';

import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Ellipsis, Search, Star } from 'lucide-react';
import { GetFeedbackList } from '@/api/data';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Details from './details';
import DateRangeFilter from '@/components/common/date-range-filter';

const FeedbackTable = () => {
  const [open, setOpen] = useState(false);
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);
  const [ratingFilter, setRatingFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // Update query parameters when search term, rating filter, or date range changes
  useEffect(() => {
    let params = [];

    if (ratingFilter && ratingFilter !== 'all') {
      params.push(`rating=${ratingFilter}`);
    }

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, ratingFilter, startDate, endDate, setQueryParam]);

  const { feedbacks, feedbacksLoading, mutate } = GetFeedbackList(
    `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
  );
  const feedbackData = feedbacks?.data?.feedbacks;
  const totalPages = feedbacks?.data?.totalPages ?? 0;

  const handleEventFromModal = (feedback: any) => {
    setDetail(feedback);
    setOpen(true);
  };

  const handleRatingChange = (value: string) => {
    setRatingFilter(value);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  // Rating options for the dropdown
  const ratingOptions = [
    { value: 'all', label: 'All Ratings' },
    { value: '1', label: 'Very Dissatisfied (1)' },
    { value: '2', label: 'Dissatisfied (2)' },
    { value: '3', label: 'Neutral (3)' },
    { value: '4', label: 'Satisfied (4)' },
    { value: '5', label: 'Very Satisfied (5)' },
  ];

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4 p-4">
          <div className="flex flex-wrap gap-3 items-center w-full md:w-auto mb-4 md:mb-0">
            <Select value={ratingFilter} onValueChange={handleRatingChange}>
              <SelectTrigger className="w-[220px]">
                <SelectValue placeholder="Filter by rating" />
              </SelectTrigger>
              <SelectContent>
                {ratingOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.value !== 'all' ? (
                      <div className="flex items-center">
                        <span className="mr-2">{option.label}</span>
                        <div className="flex">
                          {Array.from({
                            length: parseInt(option.value) || 0,
                          }).map((_, i) => (
                            <Star
                              key={i}
                              className="w-3 h-3 text-yellow-400 fill-yellow-400"
                            />
                          ))}
                        </div>
                      </div>
                    ) : (
                      option.label
                    )}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              className="w-[250px]"
            />
          </div>
          <div className="relative w-full md:w-64">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="w-4 h-4 text-gray-500" />
            </div>
            <Input
              type="text"
              placeholder="Search feedbacks..."
              className="pl-10 pr-4 py-2 w-full"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
        </div>
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Date Submitted</th>
              <th className="table-style">Type</th>
              <th className="table-style">Name</th>
              <th className="table-style">Email</th>
              <th className="table-style">Phone Number</th>
              <th className="table-style">Rating</th>
              <th className="table-style">Status</th>
              <th className="table-style">Details</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {feedbackData?.map((feedback: any, index: any) => (
              <tr
                className="text-xs text-[#062A55] dark:text-white"
                key={feedback.id}
              >
                <td className="table-style">
                  {currentPage === 1
                    ? index + 1
                    : (currentPage - 1) * pageSize + (index + 1)}
                </td>
                <td className="table-style">
                  {dayjs(feedback.createdAt).format('MMMM D, YYYY')}
                </td>
                <td className="table-style">{feedback.type}</td>
                <td className="table-style">{feedback.name}</td>
                <td className="table-style">{feedback.email}</td>
                <td className="table-style">{feedback.phone}</td>
                <td className="table-style">
                  <div className="flex">
                    {Array.from({ length: feedback.rating }).map((_, i) => (
                      <Star
                        key={i}
                        className="w-3 h-3 text-yellow-400 fill-yellow-400"
                      />
                    ))}
                  </div>
                </td>
                <td className="table-style">
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${
                      feedback.read
                        ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400'
                        : 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
                    }`}
                  >
                    {feedback.read ? 'Read' : 'Unread'}
                  </span>
                </td>
                <td className="table-style">
                  <Ellipsis
                    onClick={() => handleEventFromModal(feedback)}
                    className="w-4 h-4 cursor-pointer"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {feedbacksLoading ? (
          <LoadingState />
        ) : totalPages === 0 ? (
          <EmptyState />
        ) : null}
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      {open && (
        <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} />
      )}
    </>
  );
};

export default FeedbackTable;
