'use client';

import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Search } from 'lucide-react';
import { GetDiscountRecords } from '@/api/data';
import { numberFormat } from '@/lib/utils';
import DateRangeFilter from '@/components/common/date-range-filter';
import { Input } from '@/components/ui/input';

const DiscountRecord = () => {
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // Update query parameters when search term or date range changes
  useEffect(() => {
    let params = [];

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, startDate, endDate, setQueryParam]);

  const { discountUsage, discountUsageLoading, discountUsageError } =
    GetDiscountRecords(`?page=${currentPage}&limit=${pageSize}&${queryParam}`);
  const data = discountUsage?.data?.result;
  const totalPages = discountUsage?.data?.totalPages ?? 0;
  const totalCount = discountUsage?.data?.totalCount;

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4">
          <div className="flex flex-wrap gap-3 items-center">
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              className="w-[250px]"
            />
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search records..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
          </div>
        </div>
        <table className="whitespace-nowrap w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Date</th>
              <th className="table-style">Code</th>
              <th className="table-style">User Email</th>
              <th className="table-style">User Phone </th>
              <th className="table-style">Package</th>
              <th className="table-style">Amount Discounted</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {discountUsageLoading ? (
              <tr>
                <td colSpan={7} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading discount records...</span>
                  </div>
                </td>
              </tr>
            ) : data && data.length > 0 ? (
              data.map((value: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={value.id}
                >
                  <td className="table-style">
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </td>
                  <td className="table-style">
                    {dayjs(value.createdAt).format('MMMM D, YYYY')}
                  </td>
                  <td className="table-style">{value.code}</td>
                  <td className="table-style">{value.user.emailAddress}</td>
                  <td className="table-style">{value.user.phoneNumber}</td>
                  <td className="table-style line-clamp-1">{value.package}</td>
                  <td className="table-style">
                    {numberFormat(value.discountAmount)}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <p className="text-lg font-medium">
                      No discount records found
                    </p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Discount records will appear here once they are used'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
    </>
  );
};

export default DiscountRecord;
