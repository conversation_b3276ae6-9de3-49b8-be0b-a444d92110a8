'use client';

import React, { useState } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Ellipsis } from 'lucide-react';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { StatusBadge } from '@/components/common/status-badge';
import { GetRewards } from '@/api/reward/data';
import Create from './create';
import Details from './details';

interface RewardsProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

const RewardsTable: React.FC<RewardsProps> = ({
  openCreate,
  setOpenCreate,
}) => {
  const [open, setOpen] = useState(false);
  const { currentPage, pageSize, handlePageChange } = useSearchAndPagination({
    initialPageSize: 10,
  });

  const [detail, setDetail] = useState<any | null>(null);

  const { rewards, isLoading, mutate } = GetRewards();
  const rewardData = rewards?.data?.rewards;
  const totalPages = rewards?.totalPages;

  console.log(rewardData);

  const handleEventFromModal = (reward: any) => {
    setDetail(reward);
    setOpen(true);
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="mb-4 space-x-2"></div>
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Date Created</th>
              <th className="table-style">Location</th>
              <th className="table-style">Name</th>
              <th className="table-style">Value Type</th>
              <th className="table-style">Value</th>
              <th className="table-style">Status</th>
              <th className="table-style">Action</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {rewardData?.map((reward: any, index: any) => (
              <tr
                className="text-xs text-[#062A55] dark:text-white"
                key={reward.id}
              >
                <td className="table-style">
                  {currentPage === 1
                    ? index + 1
                    : (currentPage - 1) * pageSize + (index + 1)}
                </td>
                <td className="table-style">
                  {dayjs(reward.createdAt).format('MMMM D, YYYY')}
                </td>
                <td className="table-style">{reward.location.name}</td>
                <td className="table-style">{reward.name}</td>
                <td className="table-style">{reward.valueType}</td>
                <td className="table-style">{reward.value}</td>
                <td className="table-style">
                  {reward.deactivated
                    ? StatusBadge({ status: 'inactive' })
                    : StatusBadge({ status: 'active' })}
                </td>
                <td className="table-style">
                  <Ellipsis
                    onClick={() => handleEventFromModal(reward)}
                    className="w-4 h-4 cursor-pointer"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {isLoading ? (
          <LoadingState />
        ) : totalPages === 0 ? (
          <EmptyState />
        ) : null}
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} />
      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </>
  );
};

export default RewardsTable;
