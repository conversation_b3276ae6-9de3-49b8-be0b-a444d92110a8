import React, { useState, useMemo } from 'react';
import { InputField, FormRow, InputCalendar } from '@/components/common/form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '../../types';
import {
  RewardFormSchema,
  RewardFormValues,
} from '@/components/validations/reward';
import { CustomSelectForm } from '@/components/common/another';
import { rewardValueTypes, rewardEventTypes } from './constant';
import { MultiSelect } from '@/components/common/multi-select';
import { GetLocations } from '@/api/data';

const Create: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { locations } = GetLocations();

  const form = useForm<RewardFormValues>({
    resolver: zodResolver(RewardFormSchema),
    defaultValues: {
      description: '',
      value: '',
      name: '',
      locationIds: [],
      maxRewardsPerUser: '',
      maxRewardsPerDay: '',
    },
  });

  const locationOptions = useMemo(() => {
    return (
      locations?.data?.map((loc: any) => ({
        value: loc.id.toString(),
        label: loc.name,
      })) || []
    );
  }, [locations]);

  const onSubmit = async (data: RewardFormValues) => {
    const payload = {
      ...data,
      value: parseFloat(Number(data.value).toFixed(2)),
      maxRewardsPerUser: Number(data.maxRewardsPerUser),
      maxRewardsPerDay: Number(data.maxRewardsPerDay),
      validFrom: data.validFrom.toISOString(),
      validUntil: data.validUntil.toISOString(),
    };

    console.log(payload, 'payload');
    try {
      setIsLoading(true);
      const res = await myApi.post('/reward/create', payload);
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
      toast.error('Failed to create reward');
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add New Reward"
      description="Create a new reward for the system"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <FormRow>
            <CustomSelectForm
              control={form.control}
              name="valueType"
              label="Value Type"
              placeholder="Select reward value type"
              options={rewardValueTypes}
            />
            <CustomSelectForm
              control={form.control}
              name="eventType"
              label="Event Type"
              placeholder="Select reward event type"
              options={rewardEventTypes}
            />
            <InputField
              control={form.control}
              name="name"
              label="Reward Name"
              placeholder="Enter reward name"
              type="text"
            />
            <InputField
              control={form.control}
              name="value"
              label="Value"
              placeholder="Enter reward value"
              type="number"
              min={0}
            />
            <InputField
              control={form.control}
              name="maxRewardsPerUser"
              label="Max Rewards Per User"
              placeholder="Leave empty for unlimited"
              type="number"
              min={0}
            />
            <InputField
              control={form.control}
              name="maxRewardsPerDay"
              label="Max Rewards Per Day"
              placeholder="Leave empty for unlimited"
              type="number"
              min={0}
            />
            <InputCalendar
              control={form.control}
              name="validFrom"
              label="Valid From"
            />
            <InputCalendar
              control={form.control}
              name="validUntil"
              label="Valid Until"
            />
          </FormRow>
          <FormField
            control={form.control}
            name="locationIds"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Locations</FormLabel>
                <FormControl>
                  <MultiSelect
                    options={locationOptions}
                    selected={field.value || []}
                    onChange={field.onChange}
                    placeholder="Select locations"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <InputField
            control={form.control}
            name="description"
            label="Reward Description"
            placeholder="Enter reward description"
            type="text"
          />
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
