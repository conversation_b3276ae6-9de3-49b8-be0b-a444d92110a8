'use client';

import React, { useState, useEffect } from 'react';
import { Lightbulb, Plus, Search, Mailbox } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PERMISSIONS, hasPermission } from '@/lib/types/permissions';

import { SuggestionCard } from './components/suggestion-card';
import NewSuggestion from './components/new-suggestion';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import DateRangeFilter from '../common/date-range-filter';
import { Input } from '../ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { GetSuggestions } from '@/api/settings/data';

export default function SuggestionBox() {
  const [openNewSuggestionModal, setOpenNewSuggestionModal] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [readFilter, setReadFilter] = useState('');


  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const canEdit = hasPermission(PERMISSIONS.HUB_EDIT);

  // Build query parameters for API call
  useEffect(() => {
    const params = [];

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    if (readFilter && readFilter !== 'all') {
      params.push(`isRead=${readFilter}`);
    }

    setQueryParam(params.join('&'));
  }, [
    debouncedSearchTerm,
    startDate,
    endDate,
    readFilter,
    setQueryParam,
  ]);

  const { suggestions, isLoading, mutate } = GetSuggestions(`?page=${currentPage}&limit=${pageSize}&${queryParam}`);


  const totalPages = suggestions?.totalPages || 1;
  const suggestionData = suggestions?.suggestions || [];


  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };


  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Mailbox className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Suggestion Box 
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
              Share your thoughts or concerns and help us grow with your honest suggestions.              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 sm:border border-gray-200 dark:border-[#1F1F23]">
          <div className="space-y-6">
            <div className="flex flex-wrap items-center justify-between space-y-2">   
              <Button
                size="sm"
                onClick={() => setOpenNewSuggestionModal(true)}
                className="cursor-pointer"
              >
                <Plus className="w-3.5 h-3.5" /> New Suggestions
              </Button>
            </div>
          </div>
              <div className="flex flex-wrap gap-3 items-center my-2">
              <div className="relative w-full sm:w-64">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Search className="w-4 h-4 text-gray-500" />
                </div>
                <Input
                  type="text"
                  placeholder="Search ..."
                  className="pl-10 pr-4 py-2 w-full"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
              </div>
              <DateRangeFilter
                onDateRangeChange={handleDateRangeChange}
                className="w-full sm:w-[250px]"
              />
              <Select value={readFilter} onValueChange={setReadFilter}>
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="true">Read</SelectItem>
                  <SelectItem value="false">Not Read</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <>
            {
              isLoading ? (
                <div className='py-14 border rounded-sm text-center text-sm'>
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    <span>Loading suggestions...</span>
                  </div>
                </div>
              ) : suggestionData?.length === 0 ? (
                <div className='py-14 border rounded-sm text-center text-sm'>No data found</div>
              ) : (
                <div className="space-y-2">
                  {suggestionData?.map((dat: any, index: number) => (
                    <SuggestionCard
                      key={dat.id}
                      data={dat}
                      canEdit={canEdit}
                      mutate={mutate}
                    />
                  ))}
                  {totalPages > 1 && (
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={handlePageChange}
                    />
                  )}
                </div>
              )
            }
            </>
        </div>
      </div>
            <NewSuggestion
        open={openNewSuggestionModal}
        setOpen={setOpenNewSuggestionModal}
        mutate={mutate}
      />
    </>
  );
}
